import * as React from "react";
import ScreenWrapper from "../../../components/ScreenWrapper";
import {
  Text,
  View,
  TouchableOpacity,
  Alert,
  Platform,
  PermissionsAndroid,
} from "react-native";
import {
  usecaseSynopsisDetails,
  useLoaderAndError,
} from "../hooks/reportHooks";
import { fetchcaseSynopsis } from "../../../store/clinician/ScheduleStack/report/thunk";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import { useClinicianSelectedPatient } from "../hooks/schedulesHooks";
import { useFocusEffect } from "@react-navigation/native";
import { capitalizeName, formatReportData } from "../../../utils";
import Loader from "../../../components/Loader";
import moment from "moment";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import ViewShot from "react-native-view-shot";

interface IReportScreenProps {}

const ReportScreen: React.FunctionComponent<IReportScreenProps> = (props) => {
  const dispatch = useDispatch<AppDispatch>();
  const selectedPatient = useClinicianSelectedPatient();
  const details = usecaseSynopsisDetails();
  const reportDetails = formatReportData(details.caseSynopsis);
  const { loader } = useLoaderAndError();
  const viewShotRef = React.useRef<any>(null);
  const [screenshotLoading, setScreenshotLoading] = React.useState(false);

  // Helper function to find the largest W value in baseline measurements
  const findLargestW = (baselineTable: any) => {
    if (!baselineTable) return null;
    let largestW = 0;
    let largestWKey = null;

    Object.entries(baselineTable).forEach(([key, values]: [string, any]) => {
      if (!isNaN(Number(key)) && values?.value1) {
        const wValue = parseFloat(values.value1);
        if (wValue > largestW) {
          largestW = wValue;
          largestWKey = key;
        }
      }
    });

    return largestWKey;
  };

  // Helper function to find the lowest compression percentage in final measurements
  const findLowestCompression = (finalMeasurements: any) => {
    if (!finalMeasurements) return null;
    let lowestCompression = Infinity;
    let lowestCompressionKey = null;

    Object.entries(finalMeasurements).forEach(
      ([key, values]: [string, any]) => {
        if (!isNaN(Number(key)) && values?.per) {
          const compressionValue = parseFloat(values.per);
          if (compressionValue < lowestCompression) {
            lowestCompression = compressionValue;
            lowestCompressionKey = key;
          }
        }
      }
    );

    return lowestCompressionKey;
  };

  const largestWKey = findLargestW(reportDetails?.baselineTable);
  const lowestCompressionKey = findLowestCompression(
    reportDetails?.finalMeasurements
  );

  // Screenshot functionality
  const handleScreenshot = async () => {
    if (screenshotLoading) return;

    setScreenshotLoading(true);
    try {
      if (viewShotRef.current) {
        const uri = await viewShotRef.current.capture();
        console.log("Screenshot saved to:", uri);

        Alert.alert("Success", "Your screenshot has been saved successfully!", [
          { text: "OK", style: "default" },
        ]);
      }
    } catch (error) {
      console.error("Screenshot failed:", error);
      Alert.alert("Error", "Failed to save screenshot. Please try again.", [
        { text: "OK", style: "default" },
      ]);
    } finally {
      setScreenshotLoading(false);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          await dispatch(
            fetchcaseSynopsis({
              case_id: selectedPatient.case_id,
            })
          );
        }
      };

      fetchDetails();
    }, [selectedPatient?.case_id, dispatch])
  );

  const handleRefresh = async () => {
    if (!selectedPatient?.case_id) return;
    await dispatch(
      fetchcaseSynopsis({
        case_id: selectedPatient.case_id,
      })
    );
  };

  // Helper function to strip "Measurements" from table heading
  const getCleanTableHeading = (heading: string) => {
    if (!heading) return "";
    return heading
      .replace(/measurements?/i, "")
      .trim()
      .toUpperCase();
  };

  if (loader) {
    return <Loader />;
  }

  return (
    <ScreenWrapper direction="column" onRefresh={handleRefresh}>
      <ViewShot
        ref={viewShotRef}
        options={{
          format: "jpg",
          quality: 0.9,
          result: "tmpfile",
          snapshotContentContainer: false,
        }}
        style={{ padding: 2 }}
      >
        {/* Patient Info Grid */}
        <View className="bg-primaryWhite border border-gray-300 rounded-lg p-2 mb-2">
          <View className="flex-row mb-2">
            <View className="flex-1">
              <Text className="text-sm text-primaryBlack">
                <Text className="font-bold">PT NAME:</Text>{" "}
                {reportDetails.ptName
                  ? capitalizeName(reportDetails.ptName)
                  : "N/A"}
              </Text>
            </View>
            <View className="flex-1">
              <Text className="text-sm text-primaryBlack">
                <Text className="font-bold">DOB:</Text>{" "}
                {reportDetails?.dob
                  ? moment(reportDetails.dob).format("MM/DD/YYYY")
                  : "N/A"}
              </Text>
            </View>
          </View>

          <View className="flex-row mb-2">
            <View className="flex-1">
              <Text className="text-sm text-primaryBlack">
                <Text className="font-bold">CHA2DS2-VASC:</Text>{" "}
                {reportDetails?.CHAD || "N/A"}
              </Text>
            </View>
            <View className="flex-1">
              <Text className="text-sm text-primaryBlack">
                <Text className="font-bold">PROCEDURE:</Text>{" "}
                {reportDetails?.date
                  ? moment(reportDetails.date).format("MM/DD/YYYY")
                  : "N/A"}
              </Text>
            </View>
          </View>

          <View className="flex-row mb-2">
            <View className="flex-1">
              <Text className="text-sm text-primaryBlack">
                <Text className="font-bold">SITE:</Text>{" "}
                {reportDetails?.hospital || "N/A"}
              </Text>
            </View>
            <View className="flex-1">
              <Text className="text-sm text-primaryBlack">
                <Text className="font-bold">DEVICE:</Text>{" "}
                <Text className="font-bold text-primaryPurple bg-yellow-200 px-2 py-1 rounded">
                  {reportDetails?.device
                    ? `${reportDetails.device} ${
                        reportDetails.deviceName || ""
                      }`.trim()
                    : "N/A"}
                </Text>
              </Text>
            </View>
          </View>

          <View className="flex-row mb-2">
            <View className="flex-1">
              <Text className="text-sm text-primaryBlack">
                <Text className="font-bold">PROVIDER:</Text>{" "}
                {reportDetails?.implantingMD || "N/A"}
              </Text>
            </View>
            <View className="flex-1">
              <Text className="text-sm text-primaryBlack">
                <Text className="font-bold">SPECIALIST:</Text>{" "}
                {reportDetails?.caseSpecialist || "N/A"}
              </Text>
            </View>
          </View>

          <View className="flex-row mb-2">
            <View className="flex-1">
              <Text className="text-sm text-primaryBlack">
                <Text className="font-bold">REFERRING:</Text>{" "}
                {reportDetails?.referringProvider || "N/A"}
              </Text>
            </View>
            <View className="flex-1">
              <Text className="text-sm text-primaryBlack">
                <Text className="font-bold">PCP:</Text>{" "}
                {reportDetails?.pcp || "N/A"}
              </Text>
            </View>
          </View>

          <Text className="text-sm text-primaryBlack">
            <Text className="font-bold">RATIONALE:</Text>{" "}
            {reportDetails?.ptRationale || "N/A"}
          </Text>
        </View>

        {/* Measurement Tables - Enhanced Design */}
        <View className="flex-row mb-3">
          {/* Baseline Table */}
          {reportDetails?.baselineTable && (
            <View className="flex-1 mr-1">
              <Text className="font-bold text-center text-primaryPurple mb-2 text-base border-b-2 border-primaryPurple">
                {getCleanTableHeading(
                  reportDetails.baselineTable?.tableHeading
                ) || "BASELINE TEE"}
              </Text>
              <View className="border border-primaryBlack rounded-lg p-2 bg-primaryWhite">
                <View className="flex-row border-b-2 border-primaryPurple py-2">
                  <Text className="flex-1 font-bold text-center text-primaryPurple text-sm">
                    Angle
                  </Text>
                  <Text className="flex-1 font-bold text-center text-primaryPurple text-sm">
                    W (mm)
                  </Text>
                  <Text className="flex-1 font-bold text-center text-primaryPurple text-sm">
                    L (mm)
                  </Text>
                </View>

                {Object.entries(reportDetails.baselineTable).map(
                  ([key, values]: [string, any], index) => {
                    if (isNaN(Number(key))) return null;
                    const isLargestW = key === largestWKey;

                    const filteredLength = Object.entries(
                      reportDetails.baselineTable
                    ).filter(([k]) => !isNaN(Number(k))).length;

                    return (
                      <View
                        key={index}
                        className={`flex-row py-2 items-center ${
                          index === filteredLength - 1
                            ? "border-none"
                            : "border-b border-gray-300"
                        }`}
                      >
                        <View className="flex-1 items-center justify-center">
                          <Text className="text-center text-primaryBlack text-sm font-medium">
                            {key}°
                          </Text>
                        </View>

                        <View className="flex-1 items-center justify-center">
                          {isLargestW ? (
                            <View className="border-2 border-green-3 px-3 py-1 rounded-full">
                              <Text className="text-center text-sm font-bold text-white">
                                {values?.value1 || "N/A"}
                              </Text>
                            </View>
                          ) : (
                            <Text className="text-center text-primaryBlack text-sm font-medium">
                              {values?.value1 || "N/A"}
                            </Text>
                          )}
                        </View>

                        <View className="flex-1 items-center justify-center">
                          <Text className="text-center text-primaryBlack text-sm font-medium">
                            {values?.value2 || "N/A"}
                          </Text>
                        </View>
                      </View>
                    );
                  }
                )}
              </View>
            </View>
          )}

          {/* Final Table */}
          {reportDetails?.finalMeasurements && (
            <View className="flex-1 ml-1">
              <Text className="font-bold text-center text-primaryPurple mb-2 text-base border-b-2 border-primaryPurple">
                FINAL
              </Text>
              <View className="border border-primaryBlack rounded-lg p-2 bg-primaryWhite">
                <View className="flex-row border-b-2 border-primaryPurple py-2">
                  <Text className="flex-1 font-bold text-center text-primaryPurple text-sm">
                    Angle
                  </Text>
                  <Text className="flex-1 font-bold text-center text-primaryPurple text-sm">
                    W (mm)
                  </Text>
                  <Text className="flex-1 font-bold text-center text-primaryPurple text-sm">
                    C (%)
                  </Text>
                </View>

                {Object.entries(reportDetails.finalMeasurements)?.map(
                  ([key, values]: [string, any], index) => {
                    if (isNaN(Number(key))) return null;
                    const isLowestCompression = key === lowestCompressionKey;

                    const filteredLength = Object.entries(
                      reportDetails.finalMeasurements
                    ).filter(([k]) => !isNaN(Number(k))).length;

                    return (
                      <View
                        key={index}
                        className={`flex-row py-2 items-center ${
                          index === filteredLength - 1
                            ? "border-none"
                            : "border-b border-gray-300"
                        }`}
                      >
                        <View className="flex-1 items-center justify-center">
                          <Text className="text-center text-primaryBlack text-sm font-medium">
                            {key}°
                          </Text>
                        </View>

                        <View className="flex-1 items-center justify-center">
                          <Text className="text-center text-primaryBlack text-sm font-medium">
                            {values?.w || "N/A"}
                          </Text>
                        </View>

                        <View className="flex-1 items-center justify-center">
                          {isLowestCompression ? (
                            <View className="bg-red-500 border-2 border-green-3 px-3 py-1 rounded-full">
                              <Text className="text-center text-sm font-bold text-white">
                                {values?.per || "N/A"}
                              </Text>
                            </View>
                          ) : (
                            <Text className="text-center text-primaryBlack text-sm font-medium">
                              {values?.per || "N/A"}
                            </Text>
                          )}
                        </View>
                      </View>
                    );
                  }
                )}
              </View>
            </View>
          )}
        </View>

        {/* Procedure Details */}
        <Text className="font-bold text-center text-primaryPurple mb-2 text-base border-b-2 border-primaryPurple">
          PROCEDURE DETAILS
        </Text>
        <View className="bg-primaryWhite border border-primaryBlack rounded-lg p-2">
          <View className="flex-row mb-2">
            <View className="flex-1">
              <Text className="text-sm text-primaryBlack">
                <Text className="font-bold">LAA TYPE:</Text>{" "}
                {reportDetails?.laaType || "N/A"}
              </Text>
            </View>
            <View className="flex-1">
              <Text className="text-sm text-primaryBlack">
                <Text className="font-bold">TSP LOCATION:</Text>{" "}
                {reportDetails?.finalTspLocation
                  ? reportDetails.finalTspLocation.charAt(0).toUpperCase() +
                    reportDetails.finalTspLocation.slice(1).toLowerCase()
                  : "N/A"}
              </Text>
            </View>
          </View>

          <View className="flex-row mb-2">
            <View className="flex-1">
              <Text className="text-sm text-primaryBlack">
                <Text className="font-bold">LEAK:</Text>{" "}
                {reportDetails?.leak ? `${reportDetails.leak} mm` : "None"}
              </Text>
            </View>
            <View className="flex-1">
              <Text className="text-sm text-primaryBlack">
                <Text className="font-bold">FLUORO:</Text>{" "}
                {reportDetails?.fluoroTime
                  ? `${reportDetails.fluoroTime} min`
                  : "N/A"}
              </Text>
            </View>
          </View>

          <View className="flex-row mb-2">
            <View className="flex-1">
              <Text className="text-sm text-primaryBlack">
                <Text className="font-bold">LAP:</Text>{" "}
                {reportDetails?.lap ? `${reportDetails.lap} mmHg` : "N/A"}
              </Text>
            </View>
            <View className="flex-1">
              <Text className="text-sm text-primaryBlack">
                <Text className="font-bold">ACT:</Text>{" "}
                {reportDetails?.act ? `${reportDetails.act} sec` : "N/A"}
              </Text>
            </View>
          </View>

          <Text className="text-sm text-primaryBlack mb-2">
            <Text className="font-bold">COMPLICATIONS:</Text>{" "}
            {reportDetails?.complications || "None"}
          </Text>
          <Text className="text-sm text-primaryBlack">
            <Text className="font-bold">POST DRUG RX:</Text>{" "}
            {reportDetails?.postDrugRx
              ? reportDetails.postDrugRx
                  .map((item: string) =>
                    item.includes("Indefinitely")
                      ? item.replace("x 0", "-").trim()
                      : item
                  )
                  .join(", ")
              : "N/A"}
          </Text>
        </View>
      </ViewShot>

      {/* Screenshot Button - Bottom Right Corner */}
      <View className="absolute bottom-2 right-2">
        <TouchableOpacity
          onPress={handleScreenshot}
          disabled={screenshotLoading}
          className={`p-3 rounded-full shadow-lg ${
            screenshotLoading ? "bg-gray-400" : "bg-primaryPurple"
          }`}
        >
          <MaterialCommunityIcons
            name={screenshotLoading ? "loading" : "camera"}
            color="#FFFFFF"
            size={24}
          />
        </TouchableOpacity>
      </View>
    </ScreenWrapper>
  );
};

export default ReportScreen;
