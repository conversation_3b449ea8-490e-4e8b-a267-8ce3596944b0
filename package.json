{"name": "AtriAI", "version": "0.9.1", "sentry": true, "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "clean-android": "cd android && ./gradlew clean"}, "dependencies": {"@gorhom/bottom-sheet": "^5.0.6", "@react-native-async-storage/async-storage": "^2.1.0", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "^7.0.4", "@react-navigation/material-top-tabs": "^7.0.4", "@react-navigation/native": "^7.0.3", "@react-navigation/stack": "^7.0.3", "@reduxjs/toolkit": "^2.3.0", "@sentry/react-native": "6.11.0", "@types/react-native-vector-icons": "^6.4.18", "axios": "^1.7.7", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "moment": "^2.30.1", "nativewind": "^4.1.23", "react": "18.3.1", "react-native": "0.75.4", "react-native-app-auth": "^8.0.1", "react-native-blob-util": "^0.19.11", "react-native-calendars": "1.1307.0", "react-native-date-picker": "^5.0.7", "react-native-device-info": "^14.0.4", "react-native-document-picker": "^9.3.1", "react-native-gesture-handler": "2.21.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-pager-view": "^6.5.0", "react-native-pdf": "^6.7.5", "react-native-reanimated": "^3.17.0", "react-native-safe-area-context": "^4.14.0", "react-native-screens": "^4.1.0", "react-native-svg": "^15.9.0", "react-native-tab-view": "^4.0.1", "react-native-ui-lib": "^7.34.1", "react-native-uuid": "^2.0.3", "react-native-vector-icons": "^10.2.0", "react-native-view-shot": "^4.0.3", "react-native-webview": "^13.12.3", "react-redux": "^9.1.2", "reselect": "^5.1.1"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@babel/runtime": "^7.26.0", "@react-native/babel-preset": "0.76.2", "@react-native/eslint-config": "0.76.2", "@react-native/metro-config": "0.76.2", "@react-native/typescript-config": "0.76.2", "@types/lodash": "^4", "@types/react": "^18.3.12", "@types/react-test-renderer": "^18.3.0", "babel-jest": "^29.7.0", "eslint": "^8.57.1", "jest": "^29.7.0", "prettier": "2.8.8", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.3.1", "tailwindcss": "3.4.15", "typescript": "5.6.3"}, "engines": {"node": ">=18"}, "packageManager": "yarn@3.6.4"}